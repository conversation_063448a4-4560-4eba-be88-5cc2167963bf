{"id": 4, "title": "Interpreter", "description": "A translation agent based on a reflection agentic workflow, inspired by <PERSON>'s project: https://github.com/andrewyng/translation-agent\n\n1. Prompt an LLM to translate a text into the target language.\n2. Have the LLM reflect on the translation and provide constructive suggestions for improvement.\n3. Use these suggestions to improve the translation.", "canvas_type": "chatbot", "dsl": {"answer": [], "components": {"Answer:TinyGamesGuess": {"downstream": [], "obj": {"component_name": "Answer", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "post_answers": [], "query": []}}, "upstream": ["Generate:FuzzyEmusWork"]}, "Generate:FuzzyEmusWork": {"downstream": ["Answer:TinyGamesGuess"], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": false, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 0, "message_history_window_size": 1, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "Your task is to carefully read, then edit, a translation to {begin@lang}, taking into\naccount a list of expert suggestions and constructive criticisms.\n\nThe source text, the initial translation, and the expert linguist suggestions are delimited by XML tags <SOURCE_TEXT></SOURCE_TEXT>, <TRANSLATION></TRANSLATION> and <EXPERT_SUGGESTIONS></EXPERT_SUGGESTIONS>\nas follows:\n\n<SOURCE_TEXT>\n{begin@file}\n</SOURCE_TEXT>\n\n<TRANSLATION>\n{Generate:VastK<PERSON>sKick}\n</TRANSLATION>\n\n<EXPERT_SUGGESTIONS>\n{Generate:ShinySquidsSneeze}\n</EXPERT_SUGGESTIONS>\n\nPlease take into account the expert suggestions when editing the translation. Edit the translation by ensuring:\n\n(i) accuracy (by correcting errors of addition, mistranslation, omission, or untranslated text),\n(ii) fluency (by applying {begin@lang} grammar, spelling and punctuation rules and ensuring there are no unnecessary repetitions), \n(iii) style (by ensuring the translations reflect the style of the source text)\n(iv) terminology (inappropriate for context, inconsistent use), or\n(v) other errors.\n\nOutput only the new translation and nothing else.", "query": [], "temperature": 0.1, "top_p": 0.3}}, "upstream": ["Generate:ShinySquidsS<PERSON>ze"]}, "Generate:ShinySquidsSneeze": {"downstream": ["Generate:FuzzyEmusWork"], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": false, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 0, "message_history_window_size": 1, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "Your task is to carefully read a source text and a translation to {begin@lang}, and then give constructive criticisms and helpful suggestions to improve the translation. \n\nThe source text and initial translation, delimited by XML tags <SOURCE_TEXT></SOURCE_TEXT> and <TRANSLATION></TRANSLATION>, are as follows:\n\n<SOURCE_TEXT>\n{begin@file}\n</SOURCE_TEXT>\n\n<TRANSLATION>\n{Generate:<PERSON>ast<PERSON>eysKick}\n</TRANSLATION>\n\nWhen writing suggestions, pay attention to whether there are ways to improve the translation's \n(i) accuracy (by correcting errors of addition, mistranslation, omission, or untranslated text),\n(ii) fluency (by applying {begin@lang} grammar, spelling and punctuation rules, and ensuring there are no unnecessary repetitions),\n(iii) style (by ensuring the translations reflect the style of the source text and take into account any cultural context),\n(iv) terminology (by ensuring terminology use is consistent and reflects the source text domain; and by only ensuring you use equivalent idioms {begin@lang}).\n\nWrite a list of specific, helpful and constructive suggestions for improving the translation.\nEach suggestion should address one specific part of the translation.\nOutput only the suggestions and nothing else.", "query": [], "temperature": 0.1, "top_p": 0.3}}, "upstream": ["Generate:VastKeysKick"]}, "Generate:VastKeysKick": {"downstream": ["Generate:ShinySquidsS<PERSON>ze"], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": false, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 0, "message_history_window_size": 1, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "Role: You are a professional translator proficient in {begin@lang}, with an exceptional ability to convert specialized academic papers into accessible popular science articles. Please assist me in translating the following paragraph into {begin@lang}, ensuring that its style resembles that of popular science articles in {begin@lang}.\n\nRequirements & Restrictions:\n - Use Markdown format to output.\n - DO NOT overlook any details.\n\n\n<ORIGINAL_TEXT>\n{begin@file}\n\n<TRANSLATED_TEXT>", "query": [], "temperature": 0.1, "top_p": 0.3}}, "upstream": ["begin"]}, "begin": {"downstream": ["Generate:VastKeysKick"], "obj": {"component_name": "<PERSON><PERSON>", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "prologue": "", "query": [{"key": "lang", "name": "Target Language", "optional": false, "type": "line"}, {"key": "file", "name": "Files", "optional": false, "type": "file"}]}}, "upstream": []}}, "embed_id": "", "graph": {"edges": [{"id": "xy-edge__begin-Generate:VastKeysKickc", "markerEnd": "logo", "source": "begin", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:VastKeysKick", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:VastKeysKickb-Generate:ShinySquidsSneezec", "markerEnd": "logo", "source": "Generate:VastKeysKick", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:ShinySquidsS<PERSON>ze", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:FuzzyEmusWorkb-Answer:TinyGamesGuessc", "markerEnd": "logo", "source": "Generate:FuzzyEmusWork", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:TinyGamesGuess", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:ShinySquidsSneezeb-Generate:FuzzyEmusWorkc", "markerEnd": "logo", "source": "Generate:ShinySquidsS<PERSON>ze", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:FuzzyEmusWork", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}], "nodes": [{"data": {"form": {"prologue": "", "query": [{"key": "lang", "name": "Target Language", "optional": false, "type": "line"}, {"key": "file", "name": "Files", "optional": false, "type": "file"}]}, "label": "<PERSON><PERSON>", "name": "begin"}, "dragging": false, "height": 128, "id": "begin", "measured": {"height": 128, "width": 200}, "position": {"x": -383.5, "y": 142.62256327439624}, "positionAbsolute": {"x": -383.5, "y": 143.5}, "selected": true, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode", "width": 200}, {"data": {"form": {}, "label": "Answer", "name": "Interact_0"}, "dragging": false, "height": 44, "id": "Answer:TinyGamesGuess", "measured": {"height": 44, "width": 200}, "position": {"x": 645.5056004454161, "y": 182.98193827439627}, "positionAbsolute": {"x": 688.5, "y": 183.859375}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "logicNode", "width": 200}, {"data": {"form": {"text": "Translation Agent: Agentic translation using reflection workflow\n\nThis is inspired by <PERSON>'s project: https://github.com/andrewyng/translation-agent\n\n1. Prompt an LLM to translate a text into the target language;\n2. Have the LLM reflect on the translation and provide constructive suggestions for improvement;\n3. Use these suggestions to improve the translation."}, "label": "Note", "name": "Brief"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 227, "id": "Note:MoodyKnivesCheat", "measured": {"height": 227, "width": 703}, "position": {"x": 46.02198421645994, "y": -267.69527832581736}, "positionAbsolute": {"x": 46.02198421645994, "y": -267.69527832581736}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 227, "width": 703}, "targetPosition": "left", "type": "noteNode", "width": 703}, {"data": {"form": {"text": "Many businesses use specialized terms that are not widely used on the internet and that LLMs thus don’t know about, and there are also many terms that can be translated in multiple ways. For example, ”open source” in Spanish can be “Código abierto” or “Fuente abierta”; both are fine, but it’d better to pick one and stick with it for a single document.\n\nYou can add those glossary translation into prompt to any of `Translate directly` or 'Reflect'."}, "label": "Note", "name": "Tip: Add glossary "}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 181, "id": "Note:SourCarrotsAct", "measured": {"height": 181, "width": 832}, "position": {"x": 65.0676250238289, "y": 397.6323270065299}, "positionAbsolute": {"x": 65.0676250238289, "y": 397.6323270065299}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 181, "width": 832}, "targetPosition": "left", "type": "noteNode", "width": 832}, {"data": {"form": {"cite": false, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 1, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "Role: You are a professional translator proficient in {begin@lang}, with an exceptional ability to convert specialized academic papers into accessible popular science articles. Please assist me in translating the following paragraph into {begin@lang}, ensuring that its style resembles that of popular science articles in {begin@lang}.\n\nRequirements & Restrictions:\n - Use Markdown format to output.\n - DO NOT overlook any details.\n\n\n<ORIGINAL_TEXT>\n{begin@file}\n\n<TRANSLATED_TEXT>", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "Translate directly"}, "dragging": false, "id": "Generate:VastKeysKick", "measured": {"height": 106, "width": 200}, "position": {"x": -132.6338674989604, "y": 153.70663786774483}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}, {"data": {"form": {"cite": false, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 1, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "Your task is to carefully read a source text and a translation to {begin@lang}, and then give constructive criticisms and helpful suggestions to improve the translation. \n\nThe source text and initial translation, delimited by XML tags <SOURCE_TEXT></SOURCE_TEXT> and <TRANSLATION></TRANSLATION>, are as follows:\n\n<SOURCE_TEXT>\n{begin@file}\n</SOURCE_TEXT>\n\n<TRANSLATION>\n{Generate:<PERSON>ast<PERSON>eysKick}\n</TRANSLATION>\n\nWhen writing suggestions, pay attention to whether there are ways to improve the translation's \n(i) accuracy (by correcting errors of addition, mistranslation, omission, or untranslated text),\n(ii) fluency (by applying {begin@lang} grammar, spelling and punctuation rules, and ensuring there are no unnecessary repetitions),\n(iii) style (by ensuring the translations reflect the style of the source text and take into account any cultural context),\n(iv) terminology (by ensuring terminology use is consistent and reflects the source text domain; and by only ensuring you use equivalent idioms {begin@lang}).\n\nWrite a list of specific, helpful and constructive suggestions for improving the translation.\nEach suggestion should address one specific part of the translation.\nOutput only the suggestions and nothing else.", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "Reflect"}, "dragging": false, "id": "Generate:ShinySquidsS<PERSON>ze", "measured": {"height": 106, "width": 200}, "position": {"x": 121.*************, "y": 152.**************}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}, {"data": {"form": {"cite": false, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 1, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "Your task is to carefully read, then edit, a translation to {begin@lang}, taking into\naccount a list of expert suggestions and constructive criticisms.\n\nThe source text, the initial translation, and the expert linguist suggestions are delimited by XML tags <SOURCE_TEXT></SOURCE_TEXT>, <TRANSLATION></TRANSLATION> and <EXPERT_SUGGESTIONS></EXPERT_SUGGESTIONS>\nas follows:\n\n<SOURCE_TEXT>\n{begin@file}\n</SOURCE_TEXT>\n\n<TRANSLATION>\n{Generate:VastK<PERSON>sKick}\n</TRANSLATION>\n\n<EXPERT_SUGGESTIONS>\n{Generate:ShinySquidsSneeze}\n</EXPERT_SUGGESTIONS>\n\nPlease take into account the expert suggestions when editing the translation. Edit the translation by ensuring:\n\n(i) accuracy (by correcting errors of addition, mistranslation, omission, or untranslated text),\n(ii) fluency (by applying {begin@lang} grammar, spelling and punctuation rules and ensuring there are no unnecessary repetitions), \n(iii) style (by ensuring the translations reflect the style of the source text)\n(iv) terminology (inappropriate for context, inconsistent use), or\n(v) other errors.\n\nOutput only the new translation and nothing else.", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "Improve"}, "dragging": false, "id": "Generate:FuzzyEmusWork", "measured": {"height": 106, "width": 200}, "position": {"x": 383.*************, "y": 152.*************}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}]}, "history": [], "messages": [], "path": [], "reference": []}, "avatar": "data:image/jpeg;base64,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"}