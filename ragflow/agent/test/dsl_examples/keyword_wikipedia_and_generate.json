{"components": {"begin": {"obj": {"component_name": "<PERSON><PERSON>", "params": {"prologue": "Hi there!"}}, "downstream": ["answer:0"], "upstream": []}, "answer:0": {"obj": {"component_name": "Answer", "params": {}}, "downstream": ["keyword:0"], "upstream": ["begin"]}, "keyword:0": {"obj": {"component_name": "KeywordExtract", "params": {"llm_id": "deepseek-chat", "prompt": "- Role: You're a question analyzer.\n    - Requirements:\n     - Summarize user's question, and give top %s important keyword/phrase.\n    - Use comma as a delimiter to separate keywords/phrases.\n    - Answer format: (in language of user's question)\n    - keyword: ", "temperature": 0.2, "top_n": 1}}, "downstream": ["wikipedia:0"], "upstream": ["answer:0"]}, "wikipedia:0": {"obj": {"component_name": "Wikipedia", "params": {"top_n": 10}}, "downstream": ["generate:0"], "upstream": ["keyword:0"]}, "generate:1": {"obj": {"component_name": "Generate", "params": {"llm_id": "deepseek-chat", "prompt": "You are an intelligent assistant. Please answer the question based on content from Wikipedia. When the answer from Wikipedia is incomplete, you need to output the URL link of the corresponding content as well. When all the content searched from Wikipedia is irrelevant to the question, your answer must include the sentence, \"The answer you are looking for is not found in the Wikipedia!\". Answers need to consider chat history.\n       The content of Wikipedia is as follows:\n    {input}\n     The above is the content of Wikipedia.", "temperature": 0.2}}, "downstream": ["answer:0"], "upstream": ["wikipedia:0"]}}, "history": [], "path": [], "messages": [], "reference": {}, "answer": []}