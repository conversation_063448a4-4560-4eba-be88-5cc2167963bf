import {
  TooltipContent,
  TooltipNode,
  TooltipTrigger,
} from '@/components/xyflow/tooltip-node';
import { Position } from '@xyflow/react';
import { Copy, Play, Trash2 } from 'lucide-react';
import { Mouse<PERSON>vent<PERSON><PERSON><PERSON>, PropsWithChildren, useCallback } from 'react';
import { Operator } from '../../constant';
import { useDuplicateNode } from '../../hooks';
import useGraphStore from '../../store';

function IconWrapper({ children }: PropsWithChildren) {
  return (
    <div className="p-1.5 bg-text-title rounded-sm cursor-pointer">
      {children}
    </div>
  );
}

type ToolBarProps = {
  selected?: boolean | undefined;
  label: string;
  id: string;
} & PropsWithChildren;

export function ToolBar({ selected, children, label, id }: ToolBarProps) {
  const deleteNodeById = useGraphStore((store) => store.deleteNodeById);
  const deleteIterationNodeById = useGraphStore(
    (store) => store.deleteIterationNodeById,
  );

  const deleteNode: MouseEventHandler<SVGElement> = useCallback(
    (e) => {
      e.stopPropagation();
      if (label === Operator.Iteration) {
        deleteIterationNodeById(id);
      } else {
        deleteNodeById(id);
      }
    },
    [deleteIterationNodeById, deleteNodeById, id, label],
  );

  const duplicateNode = useDuplicateNode();

  const handleDuplicate: MouseEventHandler<SVGElement> = useCallback(
    (e) => {
      e.stopPropagation();
      duplicateNode(id, label);
    },
    [duplicateNode, id, label],
  );

  return (
    <TooltipNode selected={selected}>
      <TooltipTrigger>{children}</TooltipTrigger>

      <TooltipContent position={Position.Top}>
        <section className="flex gap-2 items-center">
          <IconWrapper>
            <Play className="size-3.5" />
          </IconWrapper>
          <IconWrapper>
            <Copy className="size-3.5" onClick={handleDuplicate} />
          </IconWrapper>
          <IconWrapper>
            <Trash2 className="size-3.5" onClick={deleteNode} />
          </IconWrapper>
        </section>
      </TooltipContent>
    </TooltipNode>
  );
}
