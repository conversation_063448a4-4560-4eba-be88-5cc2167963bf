{{- /*
TODO: Split env vars into separate secrets so that each pod
      only gets passed the secrets it really needs.
*/}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "ragflow.fullname" . }}-env-config
type: Opaque
stringData:
  {{- range $key, $val := .Values.env }}
  {{- if $val }}
  {{ $key }}: {{ quote $val }}
  {{- end }}
  {{- end }}
  {{- /*
  Use host names derived from internal cluster DNS
  */}}
  REDIS_HOST: {{ printf "%s-redis.%s.svc" (include "ragflow.fullname" .) .Release.Namespace }}
  MYSQL_HOST: {{ printf "%s-mysql.%s.svc" (include "ragflow.fullname" .) .Release.Namespace }}
  MINIO_HOST: {{ printf "%s-minio.%s.svc" (include "ragflow.fullname" .) .Release.Namespace }}
  {{- /*
  Fail if passwords are not provided in release values
  */}}
  REDIS_PASSWORD: {{ .Values.env.REDIS_PASSWORD | required "REDIS_PASSWORD is required" }}
  {{- /*
  NOTE: MySQL uses MYSQL_ROOT_PASSWORD env var but Ragflow container expects
  MYSQL_PASSWORD so we need to define both as the same value here.
  */}}
  {{- with .Values.env.MYSQL_PASSWORD | required "MYSQL_PASSWORD is required" }}
  MYSQL_PASSWORD: {{ . }}
  MYSQL_ROOT_PASSWORD: {{ . }}
  {{- end }}
  {{- with .Values.env.MINIO_PASSWORD | required "MINIO_PASSWORD is required" }}
  MINIO_PASSWORD: {{ . }}
  MINIO_ROOT_PASSWORD: {{ . }}
  {{- end }}
  {{- /*
  Only provide env vars for enabled doc engine
  */}}
  {{- if eq .Values.env.DOC_ENGINE "elasticsearch" }}
  ES_HOST: {{ printf "%s-es.%s.svc" (include "ragflow.fullname" .) .Release.Namespace }}
  ELASTIC_PASSWORD: {{ .Values.env.ELASTIC_PASSWORD | required "ELASTIC_PASSWORD is required" }}
  {{- else if eq .Values.env.DOC_ENGINE "infinity" }}
  INFINITY_HOST: {{ printf "%s-infinity.%s.svc" (include "ragflow.fullname" .) .Release.Namespace }}
  {{- else }}
  {{ fail "env.DOC_ENGINE must be either 'elasticsearch' or 'infinity'" }}
  {{- end }}
