#!/usr/bin/env python3
"""
Test script to verify the Excel tools fix for string division error.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'ration-agent', 'backend'))

async def test_path_joining():
    """Test that the path joining works correctly."""
    
    # Simulate the session workspace_path as a string (like in the real code)
    workspace_path = "/tmp/test_workspace"
    filepath = "test_file.xlsx"
    
    # Test the old way (this would fail)
    try:
        # This is what was causing the error
        old_result = str(workspace_path / filepath)
        print("ERROR: Old method should have failed but didn't!")
        return False
    except TypeError as e:
        print(f"✓ Old method correctly fails with: {e}")
    
    # Test the new way (this should work)
    try:
        new_result = str(Path(workspace_path) / filepath)
        expected = "/tmp/test_workspace/test_file.xlsx"
        if new_result == expected:
            print(f"✓ New method works correctly: {new_result}")
            return True
        else:
            print(f"ERROR: New method returned unexpected result: {new_result}")
            return False
    except Exception as e:
        print(f"ERROR: New method failed: {e}")
        return False

async def main():
    """Main test function."""
    print("Testing Excel tools path joining fix...")
    
    success = await test_path_joining()
    
    if success:
        print("\n✅ All tests passed! The fix should resolve the Excel tools error.")
    else:
        print("\n❌ Tests failed! There may still be issues.")
    
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
