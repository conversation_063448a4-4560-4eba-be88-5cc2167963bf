# Excel Tools Implementation Plan

## Overview
Add four Excel tools to the ration-agent backend for comprehensive Excel file analysis, single-file queries, multi-file joins, and manual data inspection.

## Tools to Implement

### 1. excel_metadata(filepath)
**Purpose**: Auto-detect Excel file structure and provide comprehensive metadata

**Output Structure**:
```json
{
  "file_info": {
    "sheets": ["配方表", "营养成分", "饲喂记录"],
    "file_size": "2.3MB",
    "last_modified": "2024-01-15"
  },
  "sheet_analysis": {
    "配方表": {
      "dimensions": {"rows": 145, "cols": 8},
      "data_range": "A1:H145",
      "structure": {
        "header_row": 1,
        "data_starts": 2,
        "detected_columns": [
          {"name": "配方ID", "type": "string", "sample": "CP001"},
          {"name": "蛋白质", "type": "float", "sample": 18.5}
        ],
        "merged_regions": ["A1:B1"],
        "empty_regions": ["D20:D25"],
        "data_quality": {
          "completeness": 0.95,
          "has_duplicates": false,
          "encoding": "utf-8"
        }
      }
    }
  }
}
```

### 2. excel_query(filepath, sheet, query_string)
**Purpose**: Execute pandas queries on single Excel file with high efficiency

**Features**:
- Handle complex Excel structures (merged cells, multi-headers)
- Support pandas query syntax and operations
- Auto-detect data regions and encoding
- Return structured results
- Optimized for token efficiency vs LLM writing code

**Example Usage**:
```python
excel_query("nutrition.xlsx", "配方表", "蛋白质 > 15 and 能量 < 2500")
excel_query("feeding.xlsx", "饲喂记录", "df.groupby('barn_id')['投喂量'].sum()")
```

### 3. excel_join_query(files_config, query_string)
**Purpose**: Execute complex multi-file pandas operations and joins

**Features**:
- Load multiple Excel files and sheets
- Support complex merge/join operations
- Cross-file data analysis and correlation
- Memory optimization with lazy loading
- Handle large datasets efficiently

**Example Usage**:
```python
excel_join_query([
    {"file": "nutrition.xlsx", "sheet": "配方表", "alias": "nut"},
    {"file": "feeding.xlsx", "sheet": "饲喂记录", "alias": "feed"},
    {"file": "herd_info.xlsx", "sheet": "牛群信息", "alias": "herd"}
], """
nut.merge(feed, on='配方ID')
   .merge(herd, on='barn_id')
   .assign(效率=lambda x: x['实际产量']/x['预期产量'])
   .groupby('牛群类型')['效率'].describe()
""")
```

### 4. read_excel(filepath, sheet, coordinates)
**Purpose**: Manual fallback for specific cell ranges when auto-parsing fails

**Features**:
- Support multiple coordinate formats (A1:C10, row/column numbers)
- Handle merged cells appropriately
- Raw cell value inspection
- Critical fallback when excel_metadata auto-detection fails
- Used for debugging and manual data exploration

**Example Usage**:
```python
read_excel("nutrition.xlsx", "配方表", "A1:C5")  # Header inspection
read_excel("feeding.xlsx", "记录", "1:1")        # Column names
```

## Technical Implementation

### File Location
- Implement in `ration-agent/backend/utils/tools.py`
- Create session-bound versions following existing patterns (like `create_bash_command_tool`)
- Integrate with existing session workspace and file management

### Dependencies
- Use existing openpyxl (3.1.5+) for Excel reading
- Use existing pandas (2.3.1+) for data analysis
- Handle Chinese characters and complex formatting
- Proper error handling and validation

### Memory Management (for excel_join_query)
- Lazy loading: Only load files when referenced in query
- Session-based caching: Keep DataFrames in memory during session
- Automatic cleanup: Clear unused DataFrames to prevent memory overflow
- Size limits: Handle large datasets with chunking if needed

### Tool Usage Workflow
1. **excel_metadata** - Primary entry point for understanding file structure
2. **excel_query** - Main analysis tool for single-file operations (80% of use cases)
3. **excel_join_query** - Advanced multi-file analysis and complex joins
4. **read_excel** - Manual fallback when auto-parsing fails or specific cell inspection needed

## Expected Benefits
- **Token Efficiency**: 80%+ reduction for common Excel operations vs LLM writing code
- **Performance**: Direct pandas execution vs code generation overhead
- **Reliability**: Consistent behavior with built-in error handling + manual fallback
- **Flexibility**: Handle complex nutrition spreadsheet structures and multi-file analysis
- **Robustness**: Manual reading tool ensures no Excel file is unreadable
- **Advanced Analytics**: Cross-file correlations and complex ration analysis workflows

## Integration with Existing System
- Follow existing tool creation patterns in `tools.py`
- Use session-specific file workspaces
- Integrate with current file upload system
- Add all four tools to `get_tools()` function for code worker access