import os
import json
import pandas as pd
import openpyxl
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
from langchain_core.tools import tool
from datetime import datetime
import re
import logging

logger = logging.getLogger(__name__)


async def _get_session_file_path(filepath: str, session_id: str) -> str:
    """Convert filepath to session workspace path."""
    try:
        # Import here to avoid circular imports
        from services.session_manager import session_manager
        
        # Get session workspace path
        session = await session_manager.get_session(session_id)
        if not session:
            raise RuntimeError(f"Session '{session_id}' not found")
        
        session.ensure_workspace_exists()
        
        # If filepath is already absolute and within workspace, use as-is
        if os.path.isabs(filepath) and filepath.startswith(str(session.workspace_path)):
            return filepath
        
        # Otherwise, treat as relative to workspace
        return str(Path(session.workspace_path) / filepath)
    except Exception as e:
        logger.error(f"Error getting session file path: {e}")
        raise





def _detect_rectangular_regions(worksheet) -> List[Dict[str, Any]]:
    """Efficiently detect data regions in the worksheet."""
    try:
        # Get dimensions with proper None handling and type conversion
        max_row = int(worksheet.max_row or 0)
        max_col = int(worksheet.max_column or 0)

        if max_row == 0 or max_col == 0:
            return []

        # Limit scanning to reasonable size to prevent hanging
        scan_max_row = min(max_row, 1000)  # Limit to 1000 rows
        scan_max_col = min(max_col, 100)   # Limit to 100 columns

        # Find data regions by scanning for contiguous blocks
        regions = []

        # Simple approach: find the main data region
        first_data_row = None
        last_data_row = None
        first_data_col = None
        last_data_col = None

        # Find bounds of data
        for row in range(1, scan_max_row + 1):
            for col in range(1, scan_max_col + 1):
                cell = worksheet.cell(row, col)
                if cell.value is not None and str(cell.value).strip():
                    if first_data_row is None:
                        first_data_row = row
                    last_data_row = row
                    if first_data_col is None or col < first_data_col:
                        first_data_col = col
                    if last_data_col is None or col > last_data_col:
                        last_data_col = col

        # If we found data, create a region
        if first_data_row is not None and first_data_col is not None:
            # Get sample content from the region
            sample_values = []
            sample_count = 0
            for row in range(first_data_row, min(first_data_row + 3, last_data_row + 1)):
                for col in range(first_data_col, min(first_data_col + 3, last_data_col + 1)):
                    if sample_count >= 3:
                        break
                    cell = worksheet.cell(row, col)
                    if cell.value is not None:
                        sample_values.append(str(cell.value))
                        sample_count += 1
                if sample_count >= 3:
                    break

            regions.append({
                "bounds": f"{openpyxl.utils.get_column_letter(first_data_col)}{first_data_row}:{openpyxl.utils.get_column_letter(last_data_col)}{last_data_row}",
                "sample": sample_values
            })

        return regions

    except Exception as e:
        logger.error(f"Error detecting regions: {e}")
        return []








def _analyze_sheet_structure(filepath: str, sheet_name: str) -> Dict[str, Any]:
    """Analyze Excel sheet structure with timeout protection."""
    try:
        # Load workbook for structural analysis with read-only mode for better performance
        wb = openpyxl.load_workbook(filepath, data_only=True, read_only=True)
        ws = wb[sheet_name]

        # Get dimensions and ensure they are integers
        max_row = int(ws.max_row or 0)
        max_col = int(ws.max_column or 0)

        # Safety check for very large files
        if max_row > 10000 or max_col > 1000:
            logger.warning(f"Large sheet detected ({max_row}x{max_col}), using simplified analysis")
            return {
                "dimensions": {"rows": max_row, "cols": max_col},
                "detected_regions": [{"bounds": f"A1:{openpyxl.utils.get_column_letter(min(max_col, 10))}{min(max_row, 10)}", "sample": ["Large file - use read_excel for specific ranges"]}]
            }

        # Detect data regions
        detected_regions = _detect_rectangular_regions(ws)

        return {
            "dimensions": {"rows": max_row, "cols": max_col},
            "detected_regions": detected_regions
        }

    except Exception as e:
        logger.error(f"Error analyzing sheet structure: {e}")
        return {"error": str(e)}


async def excel_metadata_impl(filepath: str, session_id: str) -> str:
    """Implementation for excel_metadata tool."""
    try:
        full_path = await _get_session_file_path(filepath, session_id)
        
        if not os.path.exists(full_path):
            return f"Error: File '{filepath}' not found in session workspace"
        
        # Get file info
        file_stat = os.stat(full_path)
        file_size_mb = round(file_stat.st_size / (1024 * 1024), 2)
        
        # Get sheet names
        try:
            excel_file = pd.ExcelFile(full_path)
            sheet_names = excel_file.sheet_names
        except Exception as e:
            return f"Error reading Excel file: {str(e)}"
        
        # Analyze each sheet
        sheet_analysis = {}
        for sheet_name in sheet_names:
            try:
                analysis = _analyze_sheet_structure(full_path, sheet_name)
                sheet_analysis[sheet_name] = analysis
            except Exception as e:
                sheet_analysis[sheet_name] = {"error": str(e)}
        
        # Build result
        result = {
            "file_info": {
                "sheets": sheet_names,
                "file_size": f"{file_size_mb}MB"
            },
            "sheet_analysis": sheet_analysis
        }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"Excel metadata analysis failed: {e}")
        return f"Error analyzing Excel file: {str(e)}"


async def excel_query_impl(filepath: str, sheet: str, query_string: str, session_id: str) -> str:
    """Implementation for excel_query tool."""
    try:
        full_path = await _get_session_file_path(filepath, session_id)
        
        if not os.path.exists(full_path):
            return f"Error: File '{filepath}' not found in session workspace"
        
        # Read Excel file
        try:
            df = pd.read_excel(full_path, sheet_name=sheet)
        except Exception as e:
            return f"Error reading Excel sheet '{sheet}': {str(e)}"
        
        # Execute query
        try:
            # Handle different query types
            if query_string.strip().startswith('df.'):
                # Direct pandas operations
                result = eval(query_string, {"df": df, "pd": pd})
            else:
                # Pandas query syntax
                result = df.query(query_string)
            
            # Format result for output
            if isinstance(result, pd.DataFrame):
                if len(result) == 0:
                    return "Query returned no results."
                elif len(result) > 100:
                    # Truncate large results
                    output = f"Query returned {len(result)} rows (showing first 100):\n\n"
                    output += result.head(100).to_string(index=False)
                else:
                    output = f"Query returned {len(result)} rows:\n\n"
                    output += result.to_string(index=False)
            elif isinstance(result, pd.Series):
                output = f"Query result:\n\n{result.to_string()}"
            else:
                output = f"Query result: {result}"
            
            return output
            
        except Exception as e:
            return f"Error executing query: {str(e)}"
    
    except Exception as e:
        logger.error(f"Excel query failed: {e}")
        return f"Error processing Excel query: {str(e)}"


async def excel_join_query_impl(files_config: List[Dict[str, str]], query_string: str, session_id: str) -> str:
    """Implementation for excel_join_query tool."""
    try:
        # Load all specified files
        dataframes = {}
        
        for config in files_config:
            filepath = config["file"]
            sheet = config["sheet"]
            alias = config["alias"]
            
            full_path = await _get_session_file_path(filepath, session_id)
            
            if not os.path.exists(full_path):
                return f"Error: File '{filepath}' not found in session workspace"
            
            try:
                df = pd.read_excel(full_path, sheet_name=sheet)
                dataframes[alias] = df
            except Exception as e:
                return f"Error reading '{filepath}' sheet '{sheet}': {str(e)}"
        
        # Execute the multi-file query
        try:
            # Create execution context with all dataframes
            exec_context = {"pd": pd, **dataframes}
            
            # Execute the query
            result = eval(query_string, exec_context)
            
            # Format result
            if isinstance(result, pd.DataFrame):
                if len(result) == 0:
                    return "Query returned no results."
                elif len(result) > 100:
                    output = f"Multi-file query returned {len(result)} rows (showing first 100):\n\n"
                    output += result.head(100).to_string(index=False)
                else:
                    output = f"Multi-file query returned {len(result)} rows:\n\n"
                    output += result.to_string(index=False)
            elif isinstance(result, pd.Series):
                output = f"Multi-file query result:\n\n{result.to_string()}"
            else:
                output = f"Multi-file query result: {result}"
            
            return output
            
        except Exception as e:
            return f"Error executing multi-file query: {str(e)}"
    
    except Exception as e:
        logger.error(f"Excel join query failed: {e}")
        return f"Error processing multi-file Excel query: {str(e)}"


async def read_excel_impl(filepath: str, sheet: str, coordinates: str, session_id: str) -> str:
    """Implementation for read_excel tool."""
    try:
        full_path = await _get_session_file_path(filepath, session_id)
        
        if not os.path.exists(full_path):
            return f"Error: File '{filepath}' not found in session workspace"
        
        # Parse coordinates
        try:
            if ':' in coordinates:
                # Range format like A1:C5
                start_cell, end_cell = coordinates.split(':')
                
                # Read with openpyxl for precise cell access
                wb = openpyxl.load_workbook(full_path, data_only=True)
                ws = wb[sheet]
                
                # Get cell range
                cell_range = ws[coordinates]
                
                # Format output
                result_rows = []
                for row in cell_range:
                    if isinstance(row, tuple):
                        # Multiple cells in row
                        row_values = [str(cell.value) if cell.value is not None else "" for cell in row]
                        result_rows.append("\t".join(row_values))
                    else:
                        # Single cell
                        result_rows.append(str(row.value) if row.value is not None else "")
                
                return f"Excel range {coordinates} from sheet '{sheet}':\n\n" + "\n".join(result_rows)
                
            elif coordinates.isdigit() or ':' in coordinates and all(part.isdigit() for part in coordinates.split(':')):
                # Row number format like "1" or "1:5"
                if ':' in coordinates:
                    start_row, end_row = map(int, coordinates.split(':'))
                    df = pd.read_excel(full_path, sheet_name=sheet, header=None, skiprows=start_row-1, nrows=end_row-start_row+1)
                else:
                    row_num = int(coordinates)
                    df = pd.read_excel(full_path, sheet_name=sheet, header=None, skiprows=row_num-1, nrows=1)
                
                return f"Excel rows {coordinates} from sheet '{sheet}':\n\n" + df.to_string(index=False, header=False)
            
            else:
                return f"Error: Invalid coordinate format '{coordinates}'. Use formats like 'A1:C5', '1:3', or '1'"
        
        except Exception as e:
            return f"Error reading Excel coordinates '{coordinates}': {str(e)}"
    
    except Exception as e:
        logger.error(f"Read Excel failed: {e}")
        return f"Error reading Excel file: {str(e)}"


def create_excel_metadata_tool(session_id: str):
    """Create excel_metadata tool bound to a specific session."""
    @tool
    async def excel_metadata(filepath: str) -> str:
        """
        Auto-detect Excel file structure and provide essential metadata.

        Analyzes an Excel file to understand its structure and detect data regions
        with their coordinates and sample content.

        Args:
            filepath: Path to the Excel file (relative to session workspace)

        Returns:
            JSON string with file info and detected regions with coordinates and samples
        """
        return await excel_metadata_impl(filepath, session_id)
    
    return excel_metadata


def create_excel_query_tool(session_id: str):
    """Create excel_query tool bound to a specific session."""
    @tool
    async def excel_query(filepath: str, sheet: str, query_string: str) -> str:
        """
        Execute pandas queries on single Excel file with high efficiency.
        
        Supports both pandas query syntax and direct pandas operations.
        Optimized for complex Excel structures and Chinese characters.
        
        Args:
            filepath: Path to the Excel file (relative to session workspace)
            sheet: Name of the sheet to query
            query_string: Pandas query string (e.g., "蛋白质 > 15" or "df.groupby('barn_id')['投喂量'].sum()")
        
        Returns:
            Formatted query results
        """
        return await excel_query_impl(filepath, sheet, query_string, session_id)
    
    return excel_query


def create_excel_join_query_tool(session_id: str):
    """Create excel_join_query tool bound to a specific session."""
    @tool
    async def excel_join_query(files_config: List[Dict[str, str]], query_string: str) -> str:
        """
        Execute complex multi-file pandas operations and joins.
        
        Load multiple Excel files and sheets, then perform complex analysis,
        merges, and cross-file operations.
        
        Args:
            files_config: List of file configurations, each with 'file', 'sheet', and 'alias' keys
            query_string: Multi-dataframe pandas query string using the aliases
        
        Returns:
            Formatted results from multi-file analysis
        
        Example:
            files_config = [
                {"file": "nutrition.xlsx", "sheet": "配方表", "alias": "nut"},
                {"file": "feeding.xlsx", "sheet": "饲喂记录", "alias": "feed"}
            ]
            query_string = "nut.merge(feed, on='配方ID').groupby('牛群类型')['效率'].mean()"
        """
        return await excel_join_query_impl(files_config, query_string, session_id)
    
    return excel_join_query


def create_read_excel_tool(session_id: str):
    """Create read_excel tool bound to a specific session."""
    @tool
    async def read_excel(filepath: str, sheet: str, coordinates: str) -> str:
        """
        Manual fallback for specific cell ranges when auto-parsing fails.
        
        Provides raw cell value inspection and handles merged cells appropriately.
        Critical fallback when excel_metadata auto-detection fails.
        
        Args:
            filepath: Path to the Excel file (relative to session workspace)
            sheet: Name of the sheet to read
            coordinates: Cell coordinates (e.g., "A1:C5", "1:3", or "1")
        
        Returns:
            Raw cell values from the specified coordinates
        """
        return await read_excel_impl(filepath, sheet, coordinates, session_id)
    
    return read_excel


async def get_excel_tools(session_id: str):
    """Get all Excel tools for a specific session."""
    return [
        create_excel_metadata_tool(session_id),
        create_excel_query_tool(session_id),
        create_excel_join_query_tool(session_id),
        create_read_excel_tool(session_id)
    ]