import os
import json
import pandas as pd
import openpyxl
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
from langchain_core.tools import tool
from datetime import datetime
import re
import logging

logger = logging.getLogger(__name__)


async def _get_session_file_path(filepath: str, session_id: str) -> str:
    """Convert filepath to session workspace path."""
    try:
        # Import here to avoid circular imports
        from services.session_manager import session_manager
        
        # Get session workspace path
        session = await session_manager.get_session(session_id)
        if not session:
            raise RuntimeError(f"Session '{session_id}' not found")
        
        session.ensure_workspace_exists()
        
        # If filepath is already absolute and within workspace, use as-is
        if os.path.isabs(filepath) and filepath.startswith(str(session.workspace_path)):
            return filepath
        
        # Otherwise, treat as relative to workspace
        return str(session.workspace_path / filepath)
    except Exception as e:
        logger.error(f"Error getting session file path: {e}")
        raise


def _detect_data_types(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Detect column data types and provide samples."""
    columns_info = []
    
    for col in df.columns:
        # Get non-null values for analysis
        non_null_values = df[col].dropna()
        
        if len(non_null_values) == 0:
            col_type = "empty"
            sample = None
        else:
            # Try to determine the best data type
            sample_value = non_null_values.iloc[0]
            
            # Check if it's numeric
            try:
                pd.to_numeric(non_null_values.iloc[:min(10, len(non_null_values))])
                if non_null_values.dtype in ['int64', 'int32', 'float64', 'float32']:
                    col_type = "numeric"
                else:
                    col_type = "numeric_string"
            except (ValueError, TypeError):
                # Check if it's datetime
                try:
                    pd.to_datetime(non_null_values.iloc[:min(5, len(non_null_values))])
                    col_type = "datetime"
                except (ValueError, TypeError):
                    col_type = "string"
            
            sample = str(sample_value)
        
        columns_info.append({
            "name": str(col),
            "type": col_type,
            "sample": sample
        })
    
    return columns_info


def _detect_rectangular_regions(worksheet) -> List[Dict[str, Any]]:
    """Use clustering to detect data regions in the worksheet."""
    try:
        # Get dimensions with proper None handling and type conversion
        max_row = int(worksheet.max_row or 0)
        max_col = int(worksheet.max_column or 0)
        
        if max_row == 0 or max_col == 0:
            return []
        
        # Get all non-empty cells
        data_cells = []
        for row in range(1, max_row + 1):
            for col in range(1, max_col + 1):
                cell = worksheet.cell(row, col)
                if cell.value is not None and str(cell.value).strip():
                    data_cells.append((row, col, cell))
        
        if not data_cells:
            return []
        
        # Use flood-fill approach to find connected regions
        regions = []
        visited = set()
        
        for row, col, cell in data_cells:
            if (row, col) not in visited:
                region_cells = _flood_fill_region(worksheet, row, col, visited)
                if len(region_cells) >= 3:  # Minimum region size
                    region_info = _analyze_region(region_cells, worksheet)
                    regions.append(region_info)
        
        return regions
        
    except Exception as e:
        logger.error(f"Error detecting regions: {e}")
        return []


def _flood_fill_region(worksheet, start_row: int, start_col: int, visited: set) -> List[tuple]:
    """Find connected non-empty cells using flood fill."""
    region = []
    stack = [(start_row, start_col)]
    
    # Get dimensions with None handling and type conversion
    max_row = int(worksheet.max_row or 0)
    max_col = int(worksheet.max_column or 0)
    
    while stack:
        row, col = stack.pop()
        
        if (row, col) in visited:
            continue
            
        # Check bounds
        if row < 1 or row > max_row or col < 1 or col > max_col:
            continue
            
        cell = worksheet.cell(row, col)
        if cell.value is None or not str(cell.value).strip():
            continue
            
        visited.add((row, col))
        region.append((row, col, cell))
        
        # Add adjacent cells (including diagonals for better region detection)
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue
                new_row, new_col = row + dr, col + dc
                if (new_row, new_col) not in visited:
                    # Check if adjacent cell should be included
                    if _should_include_in_region(worksheet, new_row, new_col, row, col):
                        stack.append((new_row, new_col))
    
    return region


def _should_include_in_region(worksheet, new_row: int, new_col: int, current_row: int, current_col: int) -> bool:
    """Determine if a cell should be included in the current region."""
    max_row = int(worksheet.max_row or 0)
    max_col = int(worksheet.max_column or 0)
    
    if new_row < 1 or new_row > max_row or new_col < 1 or new_col > max_col:
        return False
        
    new_cell = worksheet.cell(new_row, new_col)
    if new_cell.value is None or not str(new_cell.value).strip():
        return False
    
    # Include if cells are adjacent (distance <= 1 in any direction)
    row_dist = abs(new_row - current_row)
    col_dist = abs(new_col - current_col)
    
    return row_dist <= 1 and col_dist <= 1


def _analyze_region(region_cells: List[tuple], worksheet) -> Dict[str, Any]:
    """Analyze a detected region to determine its characteristics."""
    if not region_cells:
        return {}
    
    # Get bounding box
    rows = [cell[0] for cell in region_cells]
    cols = [cell[1] for cell in region_cells]
    min_row, max_row = min(rows), max(rows)
    min_col, max_col = min(cols), max(cols)
    
    # Calculate density (how filled the bounding rectangle is)
    total_cells_in_bounds = (int(max_row) - int(min_row) + 1) * (int(max_col) - int(min_col) + 1)
    density = len(region_cells) / total_cells_in_bounds if total_cells_in_bounds > 0 else 0
    
    # Analyze content to determine region type
    region_type = _classify_region_type(region_cells)
    
    # Get sample content
    sample_values = []
    for i, (row, col, cell) in enumerate(region_cells[:5]):  # First 5 cells as sample
        sample_values.append(str(cell.value))
    
    return {
        "type": region_type,
        "bounds": f"{openpyxl.utils.get_column_letter(min_col)}{min_row}:{openpyxl.utils.get_column_letter(max_col)}{max_row}",
        "sample_content": sample_values[:3]  # Only first 3 samples
    }


def _classify_region_type(region_cells: List[tuple]) -> str:
    """Classify the type of region based on content and formatting."""
    if not region_cells:
        return "unknown"
    
    # Analyze first few cells to determine type
    first_row_cells = [cell for cell in region_cells if cell[0] == min(c[0] for c in region_cells)]
    
    # Check for header characteristics
    header_indicators = 0
    for row, col, cell in first_row_cells[:5]:  # Check first 5 cells of first row
        value = str(cell.value).strip()
        # Header indicators: short text, no numbers, formatting
        if value and len(value) < 50 and not value.replace('.', '').replace(',', '').isdigit():
            if cell.font and (cell.font.bold or cell.font.size > 11):
                header_indicators += 2
            else:
                header_indicators += 1
    
    # Check for numeric data (typical of data tables)
    numeric_count = 0
    total_checked = 0
    for row, col, cell in region_cells[:20]:  # Sample first 20 cells
        if cell.value is not None:
            total_checked += 1
            try:
                float(str(cell.value).replace(',', ''))
                numeric_count += 1
            except (ValueError, TypeError):
                pass
    
    numeric_ratio = numeric_count / total_checked if total_checked > 0 else 0
    
    # Classification logic
    if len(region_cells) <= 10 and header_indicators > len(first_row_cells) * 0.6:
        return "header"
    elif numeric_ratio > 0.7:
        return "data"
    elif len(region_cells) < 20 and any("total" in str(cell[2].value).lower() or "sum" in str(cell[2].value).lower() 
                                        for cell in region_cells if cell[2].value):
        return "summary"
    elif len(region_cells) > 50:
        return "data"
    else:
        return "mixed"


def _calculate_empty_regions(detected_regions: List[Dict], max_row: int, max_col: int) -> List[Dict[str, Any]]:
    """Calculate empty regions based on detected data regions."""
    # Ensure max_row and max_col are integers
    max_row = int(max_row) if max_row is not None else 0
    max_col = int(max_col) if max_col is not None else 0
    
    if not detected_regions or max_row <= 0 or max_col <= 0:
        return []
    
    # Create a simple representation of occupied areas
    occupied_cells = set()
    for region in detected_regions:
        bounds = region.get("bounds", "")
        if ":" in bounds:
            try:
                start_cell, end_cell = bounds.split(":")
                start_col_letter = re.match(r'[A-Z]+', start_cell).group()
                start_row = int(re.search(r'\d+', start_cell).group())
                end_col_letter = re.match(r'[A-Z]+', end_cell).group()
                end_row = int(re.search(r'\d+', end_cell).group())
                
                start_col = openpyxl.utils.column_index_from_string(start_col_letter)
                end_col = openpyxl.utils.column_index_from_string(end_col_letter)
                
                for row in range(start_row, end_row + 1):
                    for col in range(start_col, end_col + 1):
                        occupied_cells.add((row, col))
            except (ValueError, AttributeError):
                continue
    
    # Find large empty areas (simplified)
    empty_regions = []
    total_cells = max_row * max_col
    occupied_count = len(occupied_cells)
    
    if occupied_count < total_cells * 0.5:  # If less than 50% occupied
        empty_percentage = round((int(total_cells) - int(occupied_count)) / int(total_cells) * 100, 1)
        empty_regions.append({
            "empty_percentage": empty_percentage
        })
    
    return empty_regions


def _analyze_sheet_structure(filepath: str, sheet_name: str) -> Dict[str, Any]:
    """Analyze Excel sheet structure using clustering-based region detection."""
    try:
        # Load workbook for structural analysis
        wb = openpyxl.load_workbook(filepath, data_only=True)
        ws = wb[sheet_name]
        
        # Get dimensions and ensure they are integers
        max_row = int(ws.max_row or 0)
        max_col = int(ws.max_column or 0)
        
        # Find merged regions
        merged_regions = [str(merged_range) for merged_range in ws.merged_cells.ranges]
        
        # NEW: Detect data regions using clustering
        detected_regions = _detect_rectangular_regions(ws)
        
        # Try to read with pandas to get data analysis
        try:
            df = pd.read_excel(filepath, sheet_name=sheet_name, header=0)
            
            # Detect columns and data quality
            columns_info = _detect_data_types(df)
            
            # Calculate data quality metrics
            total_cells = len(df) * len(df.columns)
            non_null_cells = df.count().sum()
            completeness = int(non_null_cells) / int(total_cells) if total_cells > 0 else 0
            
            # Check for duplicates
            has_duplicates = df.duplicated().any()
            
            data_quality = {
                "completeness": round(completeness, 3),
                "has_duplicates": bool(has_duplicates),
                "encoding": "utf-8"
            }
            
        except Exception as e:
            logger.warning(f"Could not analyze data quality for {sheet_name}: {e}")
            columns_info = []
            data_quality = {"error": str(e)}
        
        # Calculate empty regions based on detected data regions
        empty_regions = _calculate_empty_regions(detected_regions, max_row, max_col)
        
        # Smart header detection
        header_regions = [r for r in detected_regions if r.get('type') == 'header']
        data_regions = [r for r in detected_regions if r.get('type') == 'data']
        summary_regions = [r for r in detected_regions if r.get('type') == 'summary']
        
        # Determine likely header row
        header_row = 1  # Default
        if header_regions:
            # Use the first header region's starting row
            first_header = header_regions[0]
            bounds = first_header.get("bounds", "A1:A1")
            try:
                start_cell = bounds.split(":")[0]
                header_row = int(re.search(r'\d+', start_cell).group())
            except (ValueError, AttributeError):
                header_row = 1
        
        return {
            "dimensions": {"rows": max_row, "cols": max_col},
            "data_range": f"A1:{openpyxl.utils.get_column_letter(max_col)}{max_row}" if max_col > 0 and max_row > 0 else "A1:A1",
            "detected_regions": detected_regions,
            "structure": {
                "header_row": header_row,
                "data_starts": header_row + 1,
                "data_regions": data_regions,
                "header_regions": header_regions,
                "summary_regions": summary_regions,
                "detected_columns": columns_info,
                "merged_regions": merged_regions,
                "empty_regions": empty_regions,
                "data_quality": data_quality,
                "region_count": len(detected_regions)
            }
        }
        
    except Exception as e:
        logger.error(f"Error analyzing sheet structure: {e}")
        return {"error": str(e)}


async def excel_metadata_impl(filepath: str, session_id: str) -> str:
    """Implementation for excel_metadata tool."""
    try:
        full_path = await _get_session_file_path(filepath, session_id)
        
        if not os.path.exists(full_path):
            return f"Error: File '{filepath}' not found in session workspace"
        
        # Get file info
        file_stat = os.stat(full_path)
        file_size_mb = round(file_stat.st_size / (1024 * 1024), 2)
        last_modified = datetime.fromtimestamp(file_stat.st_mtime).strftime("%Y-%m-%d")
        
        # Get sheet names
        try:
            excel_file = pd.ExcelFile(full_path)
            sheet_names = excel_file.sheet_names
        except Exception as e:
            return f"Error reading Excel file: {str(e)}"
        
        # Analyze each sheet
        sheet_analysis = {}
        for sheet_name in sheet_names:
            try:
                analysis = _analyze_sheet_structure(full_path, sheet_name)
                sheet_analysis[sheet_name] = analysis
            except Exception as e:
                sheet_analysis[sheet_name] = {"error": str(e)}
        
        # Build result
        result = {
            "file_info": {
                "sheets": sheet_names,
                "file_size": f"{file_size_mb}MB",
                "last_modified": last_modified
            },
            "sheet_analysis": sheet_analysis
        }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"Excel metadata analysis failed: {e}")
        return f"Error analyzing Excel file: {str(e)}"


async def excel_query_impl(filepath: str, sheet: str, query_string: str, session_id: str) -> str:
    """Implementation for excel_query tool."""
    try:
        full_path = await _get_session_file_path(filepath, session_id)
        
        if not os.path.exists(full_path):
            return f"Error: File '{filepath}' not found in session workspace"
        
        # Read Excel file
        try:
            df = pd.read_excel(full_path, sheet_name=sheet)
        except Exception as e:
            return f"Error reading Excel sheet '{sheet}': {str(e)}"
        
        # Execute query
        try:
            # Handle different query types
            if query_string.strip().startswith('df.'):
                # Direct pandas operations
                result = eval(query_string, {"df": df, "pd": pd})
            else:
                # Pandas query syntax
                result = df.query(query_string)
            
            # Format result for output
            if isinstance(result, pd.DataFrame):
                if len(result) == 0:
                    return "Query returned no results."
                elif len(result) > 100:
                    # Truncate large results
                    output = f"Query returned {len(result)} rows (showing first 100):\n\n"
                    output += result.head(100).to_string(index=False)
                else:
                    output = f"Query returned {len(result)} rows:\n\n"
                    output += result.to_string(index=False)
            elif isinstance(result, pd.Series):
                output = f"Query result:\n\n{result.to_string()}"
            else:
                output = f"Query result: {result}"
            
            return output
            
        except Exception as e:
            return f"Error executing query: {str(e)}"
    
    except Exception as e:
        logger.error(f"Excel query failed: {e}")
        return f"Error processing Excel query: {str(e)}"


async def excel_join_query_impl(files_config: List[Dict[str, str]], query_string: str, session_id: str) -> str:
    """Implementation for excel_join_query tool."""
    try:
        # Load all specified files
        dataframes = {}
        
        for config in files_config:
            filepath = config["file"]
            sheet = config["sheet"]
            alias = config["alias"]
            
            full_path = await _get_session_file_path(filepath, session_id)
            
            if not os.path.exists(full_path):
                return f"Error: File '{filepath}' not found in session workspace"
            
            try:
                df = pd.read_excel(full_path, sheet_name=sheet)
                dataframes[alias] = df
            except Exception as e:
                return f"Error reading '{filepath}' sheet '{sheet}': {str(e)}"
        
        # Execute the multi-file query
        try:
            # Create execution context with all dataframes
            exec_context = {"pd": pd, **dataframes}
            
            # Execute the query
            result = eval(query_string, exec_context)
            
            # Format result
            if isinstance(result, pd.DataFrame):
                if len(result) == 0:
                    return "Query returned no results."
                elif len(result) > 100:
                    output = f"Multi-file query returned {len(result)} rows (showing first 100):\n\n"
                    output += result.head(100).to_string(index=False)
                else:
                    output = f"Multi-file query returned {len(result)} rows:\n\n"
                    output += result.to_string(index=False)
            elif isinstance(result, pd.Series):
                output = f"Multi-file query result:\n\n{result.to_string()}"
            else:
                output = f"Multi-file query result: {result}"
            
            return output
            
        except Exception as e:
            return f"Error executing multi-file query: {str(e)}"
    
    except Exception as e:
        logger.error(f"Excel join query failed: {e}")
        return f"Error processing multi-file Excel query: {str(e)}"


async def read_excel_impl(filepath: str, sheet: str, coordinates: str, session_id: str) -> str:
    """Implementation for read_excel tool."""
    try:
        full_path = await _get_session_file_path(filepath, session_id)
        
        if not os.path.exists(full_path):
            return f"Error: File '{filepath}' not found in session workspace"
        
        # Parse coordinates
        try:
            if ':' in coordinates:
                # Range format like A1:C5
                start_cell, end_cell = coordinates.split(':')
                
                # Read with openpyxl for precise cell access
                wb = openpyxl.load_workbook(full_path, data_only=True)
                ws = wb[sheet]
                
                # Get cell range
                cell_range = ws[coordinates]
                
                # Format output
                result_rows = []
                for row in cell_range:
                    if isinstance(row, tuple):
                        # Multiple cells in row
                        row_values = [str(cell.value) if cell.value is not None else "" for cell in row]
                        result_rows.append("\t".join(row_values))
                    else:
                        # Single cell
                        result_rows.append(str(row.value) if row.value is not None else "")
                
                return f"Excel range {coordinates} from sheet '{sheet}':\n\n" + "\n".join(result_rows)
                
            elif coordinates.isdigit() or ':' in coordinates and all(part.isdigit() for part in coordinates.split(':')):
                # Row number format like "1" or "1:5"
                if ':' in coordinates:
                    start_row, end_row = map(int, coordinates.split(':'))
                    df = pd.read_excel(full_path, sheet_name=sheet, header=None, skiprows=start_row-1, nrows=end_row-start_row+1)
                else:
                    row_num = int(coordinates)
                    df = pd.read_excel(full_path, sheet_name=sheet, header=None, skiprows=row_num-1, nrows=1)
                
                return f"Excel rows {coordinates} from sheet '{sheet}':\n\n" + df.to_string(index=False, header=False)
            
            else:
                return f"Error: Invalid coordinate format '{coordinates}'. Use formats like 'A1:C5', '1:3', or '1'"
        
        except Exception as e:
            return f"Error reading Excel coordinates '{coordinates}': {str(e)}"
    
    except Exception as e:
        logger.error(f"Read Excel failed: {e}")
        return f"Error reading Excel file: {str(e)}"


def create_excel_metadata_tool(session_id: str):
    """Create excel_metadata tool bound to a specific session."""
    @tool
    async def excel_metadata(filepath: str) -> str:
        """
        Auto-detect Excel file structure and provide comprehensive metadata.
        
        Analyzes an Excel file to understand its structure, sheets, column types,
        data quality, and other metadata needed for effective analysis.
        
        Args:
            filepath: Path to the Excel file (relative to session workspace)
        
        Returns:
            JSON string with comprehensive file and sheet analysis
        """
        return await excel_metadata_impl(filepath, session_id)
    
    return excel_metadata


def create_excel_query_tool(session_id: str):
    """Create excel_query tool bound to a specific session."""
    @tool
    async def excel_query(filepath: str, sheet: str, query_string: str) -> str:
        """
        Execute pandas queries on single Excel file with high efficiency.
        
        Supports both pandas query syntax and direct pandas operations.
        Optimized for complex Excel structures and Chinese characters.
        
        Args:
            filepath: Path to the Excel file (relative to session workspace)
            sheet: Name of the sheet to query
            query_string: Pandas query string (e.g., "蛋白质 > 15" or "df.groupby('barn_id')['投喂量'].sum()")
        
        Returns:
            Formatted query results
        """
        return await excel_query_impl(filepath, sheet, query_string, session_id)
    
    return excel_query


def create_excel_join_query_tool(session_id: str):
    """Create excel_join_query tool bound to a specific session."""
    @tool
    async def excel_join_query(files_config: List[Dict[str, str]], query_string: str) -> str:
        """
        Execute complex multi-file pandas operations and joins.
        
        Load multiple Excel files and sheets, then perform complex analysis,
        merges, and cross-file operations.
        
        Args:
            files_config: List of file configurations, each with 'file', 'sheet', and 'alias' keys
            query_string: Multi-dataframe pandas query string using the aliases
        
        Returns:
            Formatted results from multi-file analysis
        
        Example:
            files_config = [
                {"file": "nutrition.xlsx", "sheet": "配方表", "alias": "nut"},
                {"file": "feeding.xlsx", "sheet": "饲喂记录", "alias": "feed"}
            ]
            query_string = "nut.merge(feed, on='配方ID').groupby('牛群类型')['效率'].mean()"
        """
        return await excel_join_query_impl(files_config, query_string, session_id)
    
    return excel_join_query


def create_read_excel_tool(session_id: str):
    """Create read_excel tool bound to a specific session."""
    @tool
    async def read_excel(filepath: str, sheet: str, coordinates: str) -> str:
        """
        Manual fallback for specific cell ranges when auto-parsing fails.
        
        Provides raw cell value inspection and handles merged cells appropriately.
        Critical fallback when excel_metadata auto-detection fails.
        
        Args:
            filepath: Path to the Excel file (relative to session workspace)
            sheet: Name of the sheet to read
            coordinates: Cell coordinates (e.g., "A1:C5", "1:3", or "1")
        
        Returns:
            Raw cell values from the specified coordinates
        """
        return await read_excel_impl(filepath, sheet, coordinates, session_id)
    
    return read_excel


async def get_excel_tools(session_id: str):
    """Get all Excel tools for a specific session."""
    return [
        create_excel_metadata_tool(session_id),
        create_excel_query_tool(session_id),
        create_excel_join_query_tool(session_id),
        create_read_excel_tool(session_id)
    ]