import os
import json
import pandas as pd
import openpyxl
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
from langchain_core.tools import tool
from datetime import datetime
import re
import logging

logger = logging.getLogger(__name__)


async def _get_session_file_path(filepath: str, session_id: str) -> str:
    """Convert filepath to session workspace path."""
    try:
        # Import here to avoid circular imports
        from services.session_manager import session_manager
        
        # Get session workspace path
        session = await session_manager.get_session(session_id)
        if not session:
            raise RuntimeError(f"Session '{session_id}' not found")
        
        session.ensure_workspace_exists()
        
        # If filepath is already absolute and within workspace, use as-is
        if os.path.isabs(filepath) and filepath.startswith(str(session.workspace_path)):
            return filepath
        
        # Otherwise, treat as relative to workspace
        return str(Path(session.workspace_path) / filepath)
    except Exception as e:
        logger.error(f"Error getting session file path: {e}")
        raise





def _detect_rectangular_regions(worksheet) -> List[Dict[str, Any]]:
    """Efficiently detect data regions using openpyxl's built-in methods."""
    try:
        # Use openpyxl's efficient method to get all cells with values
        # This is much faster than iterating through every cell
        regions = []

        # Get the actual used range efficiently
        if hasattr(worksheet, 'iter_rows'):
            # Find non-empty rows and their bounds
            row_ranges = []
            current_start = None
            current_end = None

            for row_num, row in enumerate(worksheet.iter_rows(values_only=False), 1):
                # Check if row has any non-empty cells
                has_data = any(cell.value is not None and str(cell.value).strip() for cell in row)

                if has_data:
                    if current_start is None:
                        current_start = row_num
                    current_end = row_num
                else:
                    # Gap in data - save current range if it exists
                    if current_start is not None and current_end is not None:
                        if current_end - current_start >= 2:  # At least 3 rows
                            row_ranges.append((current_start, current_end))
                        current_start = None
                        current_end = None

            # Don't forget the last range
            if current_start is not None and current_end is not None:
                if current_end - current_start >= 2:
                    row_ranges.append((current_start, current_end))

            # For each row range, find column bounds
            for start_row, end_row in row_ranges:
                min_col = None
                max_col = None

                # Check a few rows to find column bounds
                check_rows = [start_row, min(start_row + 1, end_row), end_row]
                for row_num in check_rows:
                    row = list(worksheet.iter_rows(min_row=row_num, max_row=row_num, values_only=False))[0]
                    for col_num, cell in enumerate(row, 1):
                        if cell.value is not None and str(cell.value).strip():
                            if min_col is None or col_num < min_col:
                                min_col = col_num
                            if max_col is None or col_num > max_col:
                                max_col = col_num

                if min_col is not None and max_col is not None:
                    # Get sample values from the region
                    sample_values = []
                    sample_count = 0
                    for row_num in range(start_row, min(start_row + 2, end_row + 1)):
                        for col_num in range(min_col, min(min_col + 3, max_col + 1)):
                            if sample_count >= 3:
                                break
                            cell = worksheet.cell(row_num, col_num)
                            if cell.value is not None and str(cell.value).strip():
                                sample_values.append(str(cell.value))
                                sample_count += 1
                        if sample_count >= 3:
                            break

                    regions.append({
                        "bounds": f"{openpyxl.utils.get_column_letter(min_col)}{start_row}:{openpyxl.utils.get_column_letter(max_col)}{end_row}",
                        "sample": sample_values
                    })

        # Fallback: if no regions found but worksheet has dimensions, create a simple region
        if not regions and worksheet.max_row and worksheet.max_column:
            # Just sample the first few cells
            sample_values = []
            for row in range(1, min(4, worksheet.max_row + 1)):
                for col in range(1, min(4, worksheet.max_column + 1)):
                    cell = worksheet.cell(row, col)
                    if cell.value is not None and str(cell.value).strip():
                        sample_values.append(str(cell.value))
                        if len(sample_values) >= 3:
                            break
                if len(sample_values) >= 3:
                    break

            if sample_values:
                regions.append({
                    "bounds": f"A1:{openpyxl.utils.get_column_letter(worksheet.max_column)}{worksheet.max_row}",
                    "sample": sample_values
                })

        return regions

    except Exception as e:
        logger.error(f"Error detecting regions: {e}")
        return []








def _analyze_sheet_structure(filepath: str, sheet_name: str) -> Dict[str, Any]:
    """Analyze Excel sheet structure using clustering-based region detection."""
    try:
        # Load workbook for structural analysis
        wb = openpyxl.load_workbook(filepath, data_only=True)
        ws = wb[sheet_name]
        
        # Get dimensions and ensure they are integers
        max_row = int(ws.max_row or 0)
        max_col = int(ws.max_column or 0)

        # Detect data regions using clustering
        detected_regions = _detect_rectangular_regions(ws)
        
        return {
            "dimensions": {"rows": max_row, "cols": max_col},
            "detected_regions": detected_regions
        }
        
    except Exception as e:
        logger.error(f"Error analyzing sheet structure: {e}")
        return {"error": str(e)}


async def excel_metadata_impl(filepath: str, session_id: str) -> str:
    """Implementation for excel_metadata tool."""
    try:
        full_path = await _get_session_file_path(filepath, session_id)
        
        if not os.path.exists(full_path):
            return f"Error: File '{filepath}' not found in session workspace"
        
        # Get file info
        file_stat = os.stat(full_path)
        file_size_mb = round(file_stat.st_size / (1024 * 1024), 2)
        
        # Get sheet names
        try:
            excel_file = pd.ExcelFile(full_path)
            sheet_names = excel_file.sheet_names
        except Exception as e:
            return f"Error reading Excel file: {str(e)}"
        
        # Analyze each sheet
        sheet_analysis = {}
        for sheet_name in sheet_names:
            try:
                analysis = _analyze_sheet_structure(full_path, sheet_name)
                sheet_analysis[sheet_name] = analysis
            except Exception as e:
                sheet_analysis[sheet_name] = {"error": str(e)}
        
        # Build result
        result = {
            "file_info": {
                "sheets": sheet_names,
                "file_size": f"{file_size_mb}MB"
            },
            "sheet_analysis": sheet_analysis
        }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"Excel metadata analysis failed: {e}")
        return f"Error analyzing Excel file: {str(e)}"


async def excel_query_impl(filepath: str, sheet: str, query_string: str, session_id: str) -> str:
    """Implementation for excel_query tool."""
    try:
        full_path = await _get_session_file_path(filepath, session_id)
        
        if not os.path.exists(full_path):
            return f"Error: File '{filepath}' not found in session workspace"
        
        # Read Excel file
        try:
            df = pd.read_excel(full_path, sheet_name=sheet)
        except Exception as e:
            return f"Error reading Excel sheet '{sheet}': {str(e)}"
        
        # Execute query
        try:
            # Handle different query types
            if query_string.strip().startswith('df.'):
                # Direct pandas operations
                result = eval(query_string, {"df": df, "pd": pd})
            else:
                # Pandas query syntax
                result = df.query(query_string)
            
            # Format result for output
            if isinstance(result, pd.DataFrame):
                if len(result) == 0:
                    return "Query returned no results."
                elif len(result) > 100:
                    # Truncate large results
                    output = f"Query returned {len(result)} rows (showing first 100):\n\n"
                    output += result.head(100).to_string(index=False)
                else:
                    output = f"Query returned {len(result)} rows:\n\n"
                    output += result.to_string(index=False)
            elif isinstance(result, pd.Series):
                output = f"Query result:\n\n{result.to_string()}"
            else:
                output = f"Query result: {result}"
            
            return output
            
        except Exception as e:
            return f"Error executing query: {str(e)}"
    
    except Exception as e:
        logger.error(f"Excel query failed: {e}")
        return f"Error processing Excel query: {str(e)}"


async def excel_join_query_impl(files_config: List[Dict[str, str]], query_string: str, session_id: str) -> str:
    """Implementation for excel_join_query tool."""
    try:
        # Load all specified files
        dataframes = {}
        
        for config in files_config:
            filepath = config["file"]
            sheet = config["sheet"]
            alias = config["alias"]
            
            full_path = await _get_session_file_path(filepath, session_id)
            
            if not os.path.exists(full_path):
                return f"Error: File '{filepath}' not found in session workspace"
            
            try:
                df = pd.read_excel(full_path, sheet_name=sheet)
                dataframes[alias] = df
            except Exception as e:
                return f"Error reading '{filepath}' sheet '{sheet}': {str(e)}"
        
        # Execute the multi-file query
        try:
            # Create execution context with all dataframes
            exec_context = {"pd": pd, **dataframes}
            
            # Execute the query
            result = eval(query_string, exec_context)
            
            # Format result
            if isinstance(result, pd.DataFrame):
                if len(result) == 0:
                    return "Query returned no results."
                elif len(result) > 100:
                    output = f"Multi-file query returned {len(result)} rows (showing first 100):\n\n"
                    output += result.head(100).to_string(index=False)
                else:
                    output = f"Multi-file query returned {len(result)} rows:\n\n"
                    output += result.to_string(index=False)
            elif isinstance(result, pd.Series):
                output = f"Multi-file query result:\n\n{result.to_string()}"
            else:
                output = f"Multi-file query result: {result}"
            
            return output
            
        except Exception as e:
            return f"Error executing multi-file query: {str(e)}"
    
    except Exception as e:
        logger.error(f"Excel join query failed: {e}")
        return f"Error processing multi-file Excel query: {str(e)}"


async def read_excel_impl(filepath: str, sheet: str, coordinates: str, session_id: str) -> str:
    """Implementation for read_excel tool."""
    try:
        full_path = await _get_session_file_path(filepath, session_id)
        
        if not os.path.exists(full_path):
            return f"Error: File '{filepath}' not found in session workspace"
        
        # Parse coordinates
        try:
            if ':' in coordinates:
                # Range format like A1:C5
                start_cell, end_cell = coordinates.split(':')
                
                # Read with openpyxl for precise cell access
                wb = openpyxl.load_workbook(full_path, data_only=True)
                ws = wb[sheet]
                
                # Get cell range
                cell_range = ws[coordinates]
                
                # Format output
                result_rows = []
                for row in cell_range:
                    if isinstance(row, tuple):
                        # Multiple cells in row
                        row_values = [str(cell.value) if cell.value is not None else "" for cell in row]
                        result_rows.append("\t".join(row_values))
                    else:
                        # Single cell
                        result_rows.append(str(row.value) if row.value is not None else "")
                
                return f"Excel range {coordinates} from sheet '{sheet}':\n\n" + "\n".join(result_rows)
                
            elif coordinates.isdigit() or ':' in coordinates and all(part.isdigit() for part in coordinates.split(':')):
                # Row number format like "1" or "1:5"
                if ':' in coordinates:
                    start_row, end_row = map(int, coordinates.split(':'))
                    df = pd.read_excel(full_path, sheet_name=sheet, header=None, skiprows=start_row-1, nrows=end_row-start_row+1)
                else:
                    row_num = int(coordinates)
                    df = pd.read_excel(full_path, sheet_name=sheet, header=None, skiprows=row_num-1, nrows=1)
                
                return f"Excel rows {coordinates} from sheet '{sheet}':\n\n" + df.to_string(index=False, header=False)
            
            else:
                return f"Error: Invalid coordinate format '{coordinates}'. Use formats like 'A1:C5', '1:3', or '1'"
        
        except Exception as e:
            return f"Error reading Excel coordinates '{coordinates}': {str(e)}"
    
    except Exception as e:
        logger.error(f"Read Excel failed: {e}")
        return f"Error reading Excel file: {str(e)}"


def create_excel_metadata_tool(session_id: str):
    """Create excel_metadata tool bound to a specific session."""
    @tool
    async def excel_metadata(filepath: str) -> str:
        """
        Auto-detect Excel file structure and provide essential metadata.

        Analyzes an Excel file to understand its structure and detect data regions
        with their coordinates and sample content.

        Args:
            filepath: Path to the Excel file (relative to session workspace)

        Returns:
            JSON string with file info and detected regions with coordinates and samples
        """
        return await excel_metadata_impl(filepath, session_id)
    
    return excel_metadata


def create_excel_query_tool(session_id: str):
    """Create excel_query tool bound to a specific session."""
    @tool
    async def excel_query(filepath: str, sheet: str, query_string: str) -> str:
        """
        Execute pandas queries on single Excel file with high efficiency.
        
        Supports both pandas query syntax and direct pandas operations.
        Optimized for complex Excel structures and Chinese characters.
        
        Args:
            filepath: Path to the Excel file (relative to session workspace)
            sheet: Name of the sheet to query
            query_string: Pandas query string (e.g., "蛋白质 > 15" or "df.groupby('barn_id')['投喂量'].sum()")
        
        Returns:
            Formatted query results
        """
        return await excel_query_impl(filepath, sheet, query_string, session_id)
    
    return excel_query


def create_excel_join_query_tool(session_id: str):
    """Create excel_join_query tool bound to a specific session."""
    @tool
    async def excel_join_query(files_config: List[Dict[str, str]], query_string: str) -> str:
        """
        Execute complex multi-file pandas operations and joins.
        
        Load multiple Excel files and sheets, then perform complex analysis,
        merges, and cross-file operations.
        
        Args:
            files_config: List of file configurations, each with 'file', 'sheet', and 'alias' keys
            query_string: Multi-dataframe pandas query string using the aliases
        
        Returns:
            Formatted results from multi-file analysis
        
        Example:
            files_config = [
                {"file": "nutrition.xlsx", "sheet": "配方表", "alias": "nut"},
                {"file": "feeding.xlsx", "sheet": "饲喂记录", "alias": "feed"}
            ]
            query_string = "nut.merge(feed, on='配方ID').groupby('牛群类型')['效率'].mean()"
        """
        return await excel_join_query_impl(files_config, query_string, session_id)
    
    return excel_join_query


def create_read_excel_tool(session_id: str):
    """Create read_excel tool bound to a specific session."""
    @tool
    async def read_excel(filepath: str, sheet: str, coordinates: str) -> str:
        """
        Manual fallback for specific cell ranges when auto-parsing fails.
        
        Provides raw cell value inspection and handles merged cells appropriately.
        Critical fallback when excel_metadata auto-detection fails.
        
        Args:
            filepath: Path to the Excel file (relative to session workspace)
            sheet: Name of the sheet to read
            coordinates: Cell coordinates (e.g., "A1:C5", "1:3", or "1")
        
        Returns:
            Raw cell values from the specified coordinates
        """
        return await read_excel_impl(filepath, sheet, coordinates, session_id)
    
    return read_excel


async def get_excel_tools(session_id: str):
    """Get all Excel tools for a specific session."""
    return [
        create_excel_metadata_tool(session_id),
        create_excel_query_tool(session_id),
        create_excel_join_query_tool(session_id),
        create_read_excel_tool(session_id)
    ]