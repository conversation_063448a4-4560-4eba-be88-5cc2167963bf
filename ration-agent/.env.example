# LangGraph Agent Configuration
# Copy this file to .env and modify as needed

# =============================================================================
# LangGraph Recursion and Execution Limits
# =============================================================================

# Maximum number of steps/iterations the agent can take before stopping
# This prevents infinite loops and runaway executions
# Default: 100
LANGGRAPH_RECURSION_LIMIT=100

# Maximum iterations for specific operations (if needed)
# Default: 50
LANGGRAPH_MAX_ITERATIONS=50

# Timeout for agent execution in seconds
# Default: 300 (5 minutes)
LANGGRAPH_TIMEOUT=300

# =============================================================================
# Model Configuration
# =============================================================================

# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet

# Supervisor Node Model (orchestrates the workflow)
SUPERVISOR_MODEL=anthropic/claude-3.5-sonnet
SUPERVISOR_TEMPERATURE=0

# Researcher Model (handles research and search operations)
RESEARCHER_MODEL=anthropic/claude-3.5-sonnet
RESEARCHER_TEMPERATURE=0

# Coder Model (handles code generation and execution)
CODER_MODEL=anthropic/claude-3.5-sonnet
CODER_TEMPERATURE=0

# Title Generation Model (generates session titles)
TITLE_GENERATION_MODEL=anthropic/claude-3.5-sonnet
TITLE_GENERATION_TEMPERATURE=0.3

# =============================================================================
# Database Configuration
# =============================================================================

# PostgreSQL Database for LangGraph Checkpointer
DATABASE_URL=postgresql://username:password@localhost:5432/ration_agent

# =============================================================================
# API Configuration
# =============================================================================

# FastAPI Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# =============================================================================
# Logging Configuration
# =============================================================================

# Log Level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# =============================================================================
# Session Configuration
# =============================================================================

# Session timeout in seconds
SESSION_TIMEOUT=3600

# Maximum number of active sessions
MAX_ACTIVE_SESSIONS=100
